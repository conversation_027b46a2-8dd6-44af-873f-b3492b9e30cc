# React Render Debugging Guide

## How to Debug React Re-renders

### 1. **Identify the Render Pattern**
Look for timing patterns in console logs:
- **Immediate renders**: Usually prop/state changes
- **Delayed renders (100-500ms)**: Often async operations, React Query, or effects
- **Cascading renders**: Multiple renders in quick succession

### 2. **Use React DevTools Profiler**
```bash
# Install React DevTools browser extension
# Navigate to Components tab > Profiler
# Record renders and analyze the flame graph
```

### 3. **Add Custom Render Tracking**
```typescript
import { identifyRenderCause, useValueChangeTracker } from '../utils/renderDebugger';

// In your component:
const renderInfo = identifyRenderCause('ComponentName', {
  prop1,
  prop2,
}, {
  hookResult1,
  hookResult2,
});

// Track specific values:
useValueChangeTracker(suspiciousValue, 'suspiciousValue', { 
  logLevel: 'detailed' 
});
```

### 4. **Common Render Causes & Solutions**

#### **Hook Object References**
```typescript
// ❌ Problem: New object on every render
const useMyHook = () => {
  return {
    data,
    loading,
    error
  };
};

// ✅ Solution: Memoize the return object
const useMyHook = () => {
  return useMemo(() => ({
    data,
    loading,
    error
  }), [data, loading, error]);
};
```

#### **React Query Mutations**
```typescript
// ❌ Problem: useMutation returns new objects
const mutation = useMutation(options);

// ✅ Solution: Use stable mutation hook
const mutation = useStableMutation(options);
```

#### **Effect Dependencies**
```typescript
// ❌ Problem: Object in dependency array
useEffect(() => {
  // effect
}, [someObject]);

// ✅ Solution: Use specific properties
useEffect(() => {
  // effect
}, [someObject.id, someObject.status]);
```

#### **Context Value Objects**
```typescript
// ❌ Problem: New context value object
const value = {
  user,
  session,
  loading
};

// ✅ Solution: Memoize context value
const value = useMemo(() => ({
  user,
  session,
  loading
}), [user?.id, session?.access_token, loading]);
```

### 5. **Debugging Tools We've Created**

#### **useStableMutation**
Prevents React Query mutations from causing re-renders:
```typescript
const mutation = useStableMutation(mutationOptions);
```

#### **identifyRenderCause**
Comprehensive render analysis:
```typescript
const renderInfo = identifyRenderCause('ComponentName', props, hooks, context);
```

#### **useValueChangeTracker**
Track specific value changes:
```typescript
useValueChangeTracker(value, 'valueName', { logLevel: 'detailed' });
```

### 6. **Debugging Workflow**

1. **Identify the pattern**: Look at render timestamps
2. **Add render tracking**: Use `identifyRenderCause`
3. **Track suspicious hooks**: Use `useValueChangeTracker`
4. **Check React Query**: Look for mutation/query state changes
5. **Examine async operations**: Check for delayed state updates
6. **Fix object references**: Memoize hook returns and context values

### 7. **Performance Best Practices**

- Always memoize hook return objects
- Use stable references for React Query mutations
- Avoid objects in useEffect dependencies
- Memoize context values with specific dependencies
- Use React.memo for expensive components
- Implement proper loading states to prevent cascading renders

### 8. **Common Patterns to Avoid**

```typescript
// ❌ Don't do this
const hookResult = useMyHook();
useEffect(() => {
  // This will run on every render if hookResult is a new object
}, [hookResult]);

// ✅ Do this instead
const hookResult = useMyHook();
useEffect(() => {
  // Track specific properties that matter
}, [hookResult.data, hookResult.loading]);
```

The key is to ensure that values only change when their actual content changes, not just their object references.
