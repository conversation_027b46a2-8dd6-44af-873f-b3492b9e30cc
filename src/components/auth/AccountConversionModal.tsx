
import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { convertAnonymousUser, transferCertificateToExistingAccount } from '../../utils/accountConversion';
import { useCertificate } from '../../contexts/CertificateContext';
import { supabase } from '../../lib/supabase';

interface AccountConversionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  userEmail: string;
}


export const AccountConversionModal = ({
  isOpen,
  onClose,
  onSuccess,
  userEmail
}: AccountConversionModalProps) => {


  if (!isOpen) return null;
  
  // Form state
  const [currentStep, setCurrentStep] = useState<'choice' | 'login' | 'register'>('choice');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isTransferringFiles, setIsTransferringFiles] = useState(false);

  const { signIn } = useAuth();
  const { activeCertificateId } = useCertificate();

  console.log('🎭 Modal context values:', { 
    hasSignIn: !!signIn, 
    activeCertificateId 
  });
  
  // Reset modal state when opened/closed
  useEffect(() => {
    if (isOpen) {
      // Reset to initial state when modal opens
      setCurrentStep('choice');
      setPassword('');
      setConfirmPassword('');
      setError(null);
      setLoading(false);
      setShowPassword(false);
      setShowConfirmPassword(false);
      setIsTransferringFiles(false);
    }
  }, [isOpen]);

  // Handle user choice between login and register
  const handleUserChoice = useCallback((choice: 'login' | 'register') => {
    setCurrentStep(choice);
    setError(null);
  }, []);

  // Handle going back to choice step
  const handleBackToChoice = useCallback(() => {
    setCurrentStep('choice');
    setPassword('');
    setConfirmPassword('');
    setError(null);
    setShowPassword(false);
    setShowConfirmPassword(false);
  }, []);

  // Handle registration (create new account)
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!password || !confirmPassword) {
      setError('Bitte füllen Sie alle Felder aus.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Die Passwörter stimmen nicht überein.');
      return;
    }

    if (password.length < 6) {
      setError('Das Passwort muss mindestens 6 Zeichen lang sein.');
      return;
    }

    try {
      setLoading(true);
      setIsTransferringFiles(true);

      // Try to convert the anonymous user to a permanent account
      const result = await convertAnonymousUser(userEmail, password, activeCertificateId || undefined);

      if (!result.success) {
        // Check if it's an email already exists error
        if (result.error?.includes('User already registered') ||
            result.error?.includes('Email already registered') ||
            result.error?.includes('already registered')) {
          setError('Diese E-Mail-Adresse ist bereits registriert. Versuchen Sie sich anzumelden.');
          setLoading(false); 
          setIsTransferringFiles(false);
          return;
        }
        setError(result.error || 'Fehler beim Erstellen des Kontos.');
        setLoading(false); 
        setIsTransferringFiles(false);
        return;
      }

      // Log file transfer results if available
      if (result.fileTransferResult) {
        const { transferredFiles, failedFiles } = result.fileTransferResult;
        console.log(`📁 File transfer results: ${transferredFiles || 0} files transferred`);
        if (failedFiles && failedFiles.length > 0) {
          console.warn(`⚠️ Some files failed to transfer: ${failedFiles.join(', ')}`);
        }
      }

      // Verify certificate ownership after conversion
      if (activeCertificateId) {
        const { data: updatedUser } = await supabase.auth.getUser();
        const newUserId = updatedUser.user?.id;

        if (newUserId) {
          const { data: certificateData, error: fetchError } = await supabase
            .from('energieausweise')
            .select('user_id')
            .eq('id', activeCertificateId)
            .single();

          if (fetchError) {
            console.error('Error fetching certificate ownership:', fetchError);
            setError('Fehler beim Überprüfen der Zertifikatsdaten.');
            setLoading(false); 
            setIsTransferringFiles(false);
            return;
          }

          // If certificate ownership is incorrect, transfer it
          if (certificateData.user_id !== newUserId) {
            const transferResult = await transferCertificateToExistingAccount(
              activeCertificateId,
              newUserId
            );

            if (!transferResult.success) {
              console.error('Certificate transfer failed during registration:', transferResult.error);
              setError(transferResult.error || 'Fehler beim Übertragen der Zertifikatsdaten.');
              setLoading(false); 
              setIsTransferringFiles(false);
              return;
            }
          }
        }
      }

      onSuccess();
    } catch (err: any) {
      console.error('Registration error:', err);
      setError('Fehler beim Erstellen des Kontos. Bitte versuchen Sie es erneut.');
    } finally {
      setLoading(false);
      setIsTransferringFiles(false);
    }
  };

  // Handle login with existing account
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setError(null);

    if (!password) {
      setError('Bitte geben Sie Ihr Passwort ein.');
      return;
    }

    try {
      setLoading(true);
      setIsTransferringFiles(true);

      // Try to sign in with existing account
      const { data, error } = await signIn(userEmail, password);

      if (error || !data?.user) {
        // Check for specific error types
        if (error?.message?.includes('Invalid login credentials') ||
            error?.message?.includes('Invalid credentials')) {
              console.log('Invalid login credentials:', error);
          setError('Ungültiges Passwort. Bitte überprüfen Sie Ihr Passwort oder versuchen Sie sich zu registrieren.');
        } else if (error?.message?.includes('Email not confirmed')) {
          setError('Bitte bestätigen Sie Ihre E-Mail-Adresse, bevor Sie sich anmelden.');
        } else if (error?.message?.includes('User not found') ||
                   error?.message?.includes('Invalid email')) {
          setError('Diese E-Mail-Adresse ist nicht registriert. Versuchen Sie sich zu registrieren.');
        } else {
          setError('Fehler beim Anmelden. Bitte versuchen Sie es erneut.');
        }
        setLoading(false); 
        setIsTransferringFiles(false);
        console.log('state vars: ',loading, isTransferringFiles);
        return;
      }

      // Transfer certificate data to the existing account
      if (activeCertificateId) {
        console.log(`🔄 Starting certificate transfer for login: ${activeCertificateId} -> ${data.user.id}`);
        const transferResult = await transferCertificateToExistingAccount(
          activeCertificateId,
          data.user.id
        );

        if (!transferResult.success) {
          console.error('❌ Certificate transfer failed during login:', transferResult.error);
          setError(transferResult.error || 'Fehler beim Übertragen der Zertifikatsdaten.');
          setLoading(false); 
          setIsTransferringFiles(false);
          return;
        }
        console.log('✅ Certificate transfer completed successfully during login');
      }

      onSuccess();
    } catch (err: any) {
      console.error('Login error:', err);
      setError('Fehler beim Anmelden. Bitte versuchen Sie es erneut.');
    } finally {
      setLoading(false);
      setIsTransferringFiles(false);
    }
  };

  // Handle modal close
  const handleClose = useCallback(() => {
    setCurrentStep('choice');
    setPassword('');
    setConfirmPassword('');
    setError(null);
    setShowPassword(false);
    setShowConfirmPassword(false);
    setIsTransferringFiles(false);
    onClose();
  }, [onClose]);


  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">
            {currentStep === 'choice' && 'Konto erstellen oder anmelden'}
            {currentStep === 'login' && 'Anmelden'}
            {currentStep === 'register' && 'Konto erstellen'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={loading}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Step 1: Choice between Login and Register */}
        {currentStep === 'choice' && (
          <div className="space-y-4">
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                Sie haben Daten für die E-Mail-Adresse <strong>{userEmail}</strong> eingegeben.
                Haben Sie bereits ein Konto mit dieser E-Mail-Adresse oder möchten Sie ein neues Konto erstellen?
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => handleUserChoice('login')}
                className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                disabled={loading}
              >
                Ich habe bereits ein Konto - Anmelden
              </button>

              <button
                onClick={() => handleUserChoice('register')}
                className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                disabled={loading}
              >
                Neues Konto erstellen - Registrieren
              </button>
            </div>
          </div>
        )}

        {/* Step 2: Login Form */}
        {currentStep === 'login' && (
          <div className="space-y-4">
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                Melden Sie sich mit Ihrem bestehenden Konto für <strong>{userEmail}</strong> an.
                Ihre Zertifikatsdaten werden automatisch übertragen.
              </p>
            </div>

            {/* Error Display - Move it inside the login form section */}
            {error && (
              <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label htmlFor="loginPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  Passwort
                </label>
                <div className="relative">
                  <input
                    id="loginPassword"
                    name="loginPassword"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Ihr Passwort"
                    value={password}
                    onChange={(e) => {
                      console.log('⌨️ Password input onChange:', { 
                        newLength: e.target.value.length, 
                        timestamp: Date.now() 
                      });
                      setPassword(e.target.value);
                    }}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleBackToChoice}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  disabled={loading}
                >
                  Zurück
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-400 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading ? 'Anmeldung läuft...' : 'Anmelden'}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Step 2: Register Form */}
        {currentStep === 'register' && (
          <div className="space-y-4">
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-700">
                Erstellen Sie ein neues Konto für <strong>{userEmail}</strong>.
                So können Sie Ihre Energieausweise später verwalten.
              </p>
            </div>

            {/* Error Display - Move it inside the register form section */}
            {error && (
              <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            <form onSubmit={handleRegister} className="space-y-4">
              <div>
                <label htmlFor="registerPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  Passwort
                </label>
                <div className="relative">
                  <input
                    id="registerPassword"
                    name="registerPassword"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Mindestens 6 Zeichen"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  Passwort bestätigen
                </label>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Passwort wiederholen"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleBackToChoice}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  disabled={loading}
                >
                  Zurück
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-green-400 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading ? (isTransferringFiles ? 'Konto wird erstellt...' : 'Erstellen...') : 'Konto erstellen'}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Loading Indicator */}
        {loading && isTransferringFiles && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
              <span className="text-sm text-blue-700">
                Konto wird erstellt und Dateien werden übertragen...
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
