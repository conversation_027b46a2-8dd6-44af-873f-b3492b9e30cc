import React, { useRef, useEffect } from 'react';
import { DataSection } from '../ui/DataSection';
import { useCertificate } from '../../contexts/CertificateContext';
import type { SectionConfig } from '../../hooks/useSummaryData';

export const DataSummaryDisplay: React.FC<{ sections?: SectionConfig[] | null }> = ({ sections }) => {
  const { activeCertificateId, certificateFiles } = useCertificate();

  const renderCountRef = useRef(0);
  renderCountRef.current += 1;
  console.log(`DataSummaryDisplay useRef render #${renderCountRef.current}`);
  useEffect(() => {
    console.log(`DataSummaryDisplay useEffect render #${renderCountRef.current}`);
  });

  if (!activeCertificateId || !sections || sections.length === 0) return null;

  return (
    <div className="space-y-8 mb-8">
      {sections.map((section, index) => (
        <DataSection
          key={`${section.title}-${index}`}
          title={section.title}
          data={section.data}
          fields={section.fields}
          excludeFields={section.excludeFields}
          filesByField={certificateFiles}
        />
      ))}
    </div>
  );
};