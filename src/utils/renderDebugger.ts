import { useRef, useEffect } from 'react';

/**
 * Comprehensive React render debugging utilities
 */

export interface RenderDebugOptions {
  componentName: string;
  logLevel?: 'minimal' | 'detailed' | 'verbose';
  trackProps?: boolean;
  trackHooks?: boolean;
  trackCallStack?: boolean;
}

/**
 * Hook to track and debug React component re-renders
 * Provides detailed information about what caused each render
 */
export function useRenderDebugger(
  dependencies: Record<string, any>,
  options: RenderDebugOptions
) {
  const renderCountRef = useRef(0);
  const prevDepsRef = useRef<Record<string, any>>({});
  const renderTimestampRef = useRef<number>(Date.now());

  renderCountRef.current += 1;
  const currentTimestamp = Date.now();
  const timeSinceLastRender = currentTimestamp - renderTimestampRef.current;
  renderTimestampRef.current = currentTimestamp;

  // Analyze what changed
  const changes: Array<{
    key: string;
    previous: any;
    current: any;
    type: 'primitive' | 'object' | 'function' | 'array';
    hasChanged: boolean;
  }> = [];

  Object.entries(dependencies).forEach(([key, value]) => {
    const prevValue = prevDepsRef.current[key];
    const hasChanged = prevValue !== value;
    
    let type: 'primitive' | 'object' | 'function' | 'array' = 'primitive';
    if (typeof value === 'function') type = 'function';
    else if (Array.isArray(value)) type = 'array';
    else if (typeof value === 'object' && value !== null) type = 'object';

    changes.push({
      key,
      previous: prevValue,
      current: value,
      type,
      hasChanged
    });
  });

  const changedDeps = changes.filter(change => change.hasChanged);

  // Log render information
  useEffect(() => {
    const { componentName, logLevel = 'detailed' } = options;
    
    if (logLevel === 'minimal' && changedDeps.length === 0) {
      return; // Skip logging if no changes and minimal logging
    }

    console.group(`🔍 ${componentName} render #${renderCountRef.current}`);
    console.log('Timestamp:', new Date().toISOString());
    console.log('Time since last render:', `${timeSinceLastRender}ms`);
    
    if (changedDeps.length > 0) {
      console.log('🔄 Dependencies that changed:');
      changedDeps.forEach(({ key, previous, current, type }) => {
        console.log(`  ✅ ${key} (${type}):`, {
          previous,
          current,
          referenceChanged: previous !== current,
          deepEqual: type === 'object' ? JSON.stringify(previous) === JSON.stringify(current) : 'N/A'
        });
      });
    } else {
      console.log('⚠️ No dependency changes detected - investigating phantom render');
      
      if (logLevel === 'verbose') {
        console.log('All dependencies:', dependencies);
        
        if (options.trackCallStack) {
          console.log('Call stack:', new Error().stack);
        }
      }
    }

    console.groupEnd();
  });

  // Store current deps for next comparison
  prevDepsRef.current = { ...dependencies };

  return {
    renderCount: renderCountRef.current,
    timeSinceLastRender,
    changedDependencies: changedDeps,
    hasChanges: changedDeps.length > 0
  };
}

/**
 * Hook to track when a specific value changes and log detailed information
 */
export function useValueChangeTracker<T>(
  value: T,
  valueName: string,
  options: { logLevel?: 'minimal' | 'detailed'; deepCompare?: boolean } = {}
) {
  const prevValueRef = useRef<T>(value);
  const changeCountRef = useRef(0);

  const hasChanged = prevValueRef.current !== value;
  const deepEqual = options.deepCompare && typeof value === 'object' 
    ? JSON.stringify(prevValueRef.current) === JSON.stringify(value)
    : null;

  if (hasChanged) {
    changeCountRef.current += 1;
    
    if (options.logLevel !== 'minimal') {
      console.log(`🔄 ${valueName} changed (#${changeCountRef.current}):`, {
        previous: prevValueRef.current,
        current: value,
        referenceChanged: true,
        deepEqual,
        timestamp: new Date().toISOString()
      });
    }
    
    prevValueRef.current = value;
  }

  return {
    hasChanged,
    changeCount: changeCountRef.current,
    deepEqual
  };
}

/**
 * Utility to identify what caused a React component to re-render
 * Call this at the top of your component to get render cause analysis
 */
export function identifyRenderCause(
  componentName: string,
  props: Record<string, any> = {},
  hooks: Record<string, any> = {},
  context: Record<string, any> = {}
) {
  const allDependencies = {
    ...Object.fromEntries(Object.entries(props).map(([key, value]) => [`prop:${key}`, value])),
    ...Object.fromEntries(Object.entries(hooks).map(([key, value]) => [`hook:${key}`, value])),
    ...Object.fromEntries(Object.entries(context).map(([key, value]) => [`context:${key}`, value]))
  };

  return useRenderDebugger(allDependencies, {
    componentName,
    logLevel: 'detailed',
    trackProps: true,
    trackHooks: true,
    trackCallStack: false
  });
}
