// Standardized query key factory
export const certificateQueryKeys = {
  all: ['certificates'] as const,
  lists: () => [...certificateQueryKeys.all, 'list'] as const,
  list: (filters: string) => [...certificateQueryKeys.lists(), { filters }] as const,
  details: () => [...certificateQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...certificateQueryKeys.details(), id] as const,
  files: (id: string) => [...certificateQueryKeys.detail(id), 'files'] as const,
  pricing: (type: string) => ['pricing', type] as const,
};

export default certificateQueryKeys;
