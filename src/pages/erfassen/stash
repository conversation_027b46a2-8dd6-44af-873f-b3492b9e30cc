  <SummaryHeader />
        
        {/* Payment Status Banner */}
        <PaymentStatusBanner />

        {/* Data Summary Display - sections computed by parent to avoid duplicate hook calls */}
        <DataSummaryDisplay sections={sections} />

        {/* Pricing Overview (context-only) */}
        <PricingOverview />

        {/* Certificate Ordering Information */}
        <CertificateOrderingInfo />

        {/* Legal Consent Section - consumes consent via hook */}
        {!isPaid && <LegalConsentSection />}

        {/* Action Buttons (context-only) */}
        <SummaryActions />

        {/* Account Conversion Modal for Anonymous Users */}
        {isAnonymous && userEmail && showAccountConversion && (
          <AccountConversionModal
            isOpen={showAccountConversion}
            onClose={onAccountConversionClose}
            onSuccess={onAccountConversionSuccess}
            userEmail={userEmail}
          />
        )}