import React, { useMemo, useRef, useEffect, useLayoutEffect } from 'react';
import { useCertificate } from '../../contexts/CertificateContext';
import { useAuth } from '../../contexts/AuthContext';
import { usePageVisit } from '../../hooks/usePageVisit';
import { AccountConversionModal } from '../../components/auth/AccountConversionModal';

// Import custom hooks
import { useSummaryData } from '../../hooks/useSummaryData';
import { usePaymentFlow } from '../../hooks/usePaymentFlow';
import { useLegalConsent } from '../../hooks/useLegalConsent';
import { useAccountConversion } from '../../hooks/useAccountConversion';

// Debug helper to trace render causes
const useRenderTracker = (componentName: string) => {
  const renderCountRef = useRef(0);
  const prevDepsRef = useRef<Record<string, any>>({});
  
  return (deps: Record<string, any>) => {
    renderCountRef.current += 1;
    
    // Log on layout effect to ensure we catch all renders
    useLayoutEffect(() => {
      console.group(`🔍 ${componentName} render #${renderCountRef.current}`);
      console.log('Render timestamp:', new Date().toISOString());
      
      // Check which dependencies changed
      Object.entries(deps).forEach(([key, value]) => {
        const prevValue = prevDepsRef.current[key];
        const hasChanged = prevValue !== value;
        
        if (hasChanged) {
          console.log(`✅ Changed: ${key}`);
          console.log('  Previous:', prevValue);
          console.log('  Current:', value);
        }
      });
      
      // Store current deps for next comparison
      prevDepsRef.current = { ...deps };
      console.groupEnd();
    });
    
    return renderCountRef.current;
  };
};

// Import presentational components
import {
  SummaryHeader,
  PaymentStatusBanner,
  DataSummaryDisplay,
  PricingOverview,
  LegalConsentSection,
  SummaryActions
} from '../../components/summary';
import CertificateOrderingInfo from '../../components/CertificateOrderingInfo';


/**
 * Refactored ZusammenfassungPage component using the new architecture
 * This component now orchestrates custom hooks and presentational components
 * instead of handling all business logic internally
 *
 * PERFORMANCE OPTIMIZATIONS:
 * - Memoized certificate ID to prevent unnecessary hook re-executions
 * - Debounced page visit tracking to prevent rapid database updates
 * - Reduced console logging to essential renders only
 */
export const ZusammenfassungPage: React.FC = () => {
  // Context hooks with memoization to prevent unnecessary re-renders
  const { activeCertificateId } = useCertificate();
  const { isAnonymous } = useAuth();

  // Memoize certificate ID to prevent hook dependency cascades
  const memoizedCertificateId = useMemo(() => activeCertificateId, [activeCertificateId]);

  // Enhanced render tracking with dependency monitoring
  const trackRender = useRenderTracker('ZusammenfassungPage');
  
  // Track all dependencies that might cause re-renders
  trackRender({
    activeCertificateId,
    isAnonymous,
    memoizedCertificateId,
  });

  // Page visit tracking with debounced execution
  usePageVisit('zusammenfassung');

  // Custom hooks for business logic - using memoized certificate ID to prevent cascading updates
  const summaryDataResult = useSummaryData(memoizedCertificateId);
  const {
    energieausweisData,
    isPaid,
    isError,
    sections,
  } = summaryDataResult;

  // Track when useSummaryData returns change (using ref to avoid re-renders)
  const summaryDataRef = useRef(summaryDataResult);
  if (summaryDataRef.current !== summaryDataResult) {
    console.log('🔍 useSummaryData result changed:', {
      hasData: !!energieausweisData,
      dataId: energieausweisData?.id,
      dataStatus: energieausweisData?.status,
      isPaid,
      isError,
      sectionsCount: Object.keys(sections || {}).length
    });
    summaryDataRef.current = summaryDataResult;
  }

  // Track post-hook dependencies with more specific logging
  useEffect(() => {
    console.group('🔍 ZusammenfassungPage Hook Dependencies');
    console.log('energieausweisData ID:', energieausweisData?.id);
    console.log('energieausweisData status:', energieausweisData?.status);
    console.log('isPaid:', isPaid);
    console.log('isError:', isError);
    console.log('sections keys:', Object.keys(sections || {}));
    console.log('sections length:', Object.keys(sections || {}).length);
    console.groupEnd();
  }, [
    energieausweisData?.id, // More specific dependency
    energieausweisData?.status, // More specific dependency
    isPaid,
    isError,
    Object.keys(sections || {}).length // Track sections length instead of full object
  ]);

  // Memoize energieausweisData with stricter equality checks
  const memoizedEnergiausweisData = useMemo(() => energieausweisData, [
    energieausweisData?.id,
    energieausweisData?.status,
    energieausweisData?.certificate_type
  ]);

  const paymentFlowResult = usePaymentFlow(memoizedCertificateId, memoizedEnergiausweisData);
  const {
    handleCheckout,
  } = paymentFlowResult;

  // Track when usePaymentFlow returns change (using ref to avoid re-renders)
  const paymentFlowRef = useRef(paymentFlowResult);
  if (paymentFlowRef.current !== paymentFlowResult) {
    console.log('🔍 usePaymentFlow result changed');
    paymentFlowRef.current = paymentFlowResult;
  }

  // Legal consent is consumed by LegalConsentSection directly now
  const accountConversionResult = useAccountConversion(memoizedCertificateId);
  const {
    showAccountConversion,
    userEmail,
    setShowAccountConversion,
    handleAccountConversionSuccess,
    handleAccountConversionClose
  } = accountConversionResult;

  // Track when useAccountConversion returns change (using ref to avoid re-renders)
  const accountConversionRef = useRef(accountConversionResult);
  if (accountConversionRef.current !== accountConversionResult) {
    console.log('🔍 useAccountConversion result changed:', {
      showAccountConversion,
      hasUserEmail: !!userEmail
    });
    accountConversionRef.current = accountConversionResult;
  }

  // We still need legalConsent here to resume checkout after account conversion
  const legalConsentResult = useLegalConsent();
  const { legalConsent } = legalConsentResult;

  // Track when useLegalConsent returns change (using ref to avoid re-renders)
  const legalConsentRef = useRef(legalConsentResult);
  if (legalConsentRef.current !== legalConsentResult) {
    console.log('🔍 useLegalConsent result changed:', {
      isValid: legalConsentResult.isValid,
      consentState: Object.entries(legalConsent).filter(([_, value]) => value).map(([key]) => key)
    });
    legalConsentRef.current = legalConsentResult;
  }

  // File management handled by DataSummaryDisplay via context

  // Memoize callback functions to prevent unnecessary re-renders of child components
  const onAccountConversionSuccess = useMemo(() => () => {
    handleAccountConversionSuccess(() => {
      // Retry checkout after successful account conversion
      handleCheckout(legalConsent, false, () => setShowAccountConversion(true));
    });
  }, [handleAccountConversionSuccess, handleCheckout, legalConsent, setShowAccountConversion]);

  const onAccountConversionClose = useMemo(() => () => {
    // pass a no-op cleanup callback as the hook expects a callback
    handleAccountConversionClose(() => {});
  }, [handleAccountConversionClose]);

  // Provider-level loading skeleton is shown by CertificateProvider.

  // Error state
  if (isError || !energieausweisData) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Fehler beim Laden der Daten
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>
                    Die Energieausweis-Daten konnten nicht geladen werden. 
                    Bitte versuchen Sie es erneut oder wenden Sie sich an den Support.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Main render
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Header Section */}
      
      </div>
    </div>
  );
};
