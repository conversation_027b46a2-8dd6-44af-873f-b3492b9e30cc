import { Router<PERSON>rovider } from '@tanstack/react-router';
import { router } from './routes';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { CertificateProvider } from './contexts/CertificateContext';
import { useEffect, useMemo, useRef } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client with optimized global defaults to prevent excessive re-renders
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Prevent refetching on window focus globally - key optimization for tab switching
      refetchOnWindowFocus: false,
      // Only refetch if data is stale, not on every mount
      refetchOnMount: false,
      // Don't refetch on network reconnect unless explicitly needed
      refetchOnReconnect: false,
      // Set reasonable stale time to prevent unnecessary refetches
      staleTime: 5 * 60 * 1000, // 5 minutes
      // Keep data in cache longer to improve performance
      gcTime: 15 * 60 * 1000, // 15 minutes (increased from 10 minutes)
      // Reduce retry attempts for better performance
      retry: 1,
      // Shorter delay between retries
      retryDelay: 1500, // Slightly longer delay to reduce server load
      // Disable automatic background refetching
      refetchInterval: false,
      refetchIntervalInBackground: false,
    },
    mutations: {
      // Reduce retry attempts for mutations
      retry: 1,
      retryDelay: 1500, // Match query retry delay
    },
  },
});

function AppWithAuth() {
  const { user, session, loading } = useAuth();

  // Memoize router context to prevent unnecessary updates
  const routerContext = useMemo(() => ({
    user,
    session,
    loading,
  }), [user?.id, session?.access_token, loading]); // More specific dependencies

// Only call router.update when auth values meaningfully change.
  // This prevents router.update from being invoked with a new object identity
  // that doesn't actually carry different auth info.
  const prevAuthRef = useRef<{ userId: string | null; accessToken: string | null; loading: boolean | null } | null>(null);
  useEffect(() => {
    // wait for auth resolution
    if (loading) return;

    const next = {
      userId: user?.id ?? null,
      accessToken: session?.access_token ?? null,
      loading: false,
    };

    const prev = prevAuthRef.current;
    if (prev && prev.userId === next.userId && prev.accessToken === next.accessToken && prev.loading === next.loading) {
      // no meaningful change -> skip router.update
      return;
    }

    prevAuthRef.current = next;
    console.log('Updating router context with:', routerContext);
    router.update({
      context: routerContext,
    });
  }, [user?.id, session?.access_token, loading, routerContext]);

    return (
      <>
        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="text-center bg-white p-6 rounded-lg">
              <div className="w-16 h-16 border-t-4 border-green-500 border-solid rounded-full animate-spin mx-auto"></div>
              <p className="mt-4 text-gray-600">Credentials werden geprüft...</p>
            </div>
          </div>
        )}
        <RouterProvider router={router} />
      </>
    );
    
}

function App() {
  return (
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        <CertificateProvider>
          <AppWithAuth />
        </CertificateProvider>
        {/* React Query DevTools - only in development */}
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </AuthProvider>
  );
}

export default App;
