import { supabase } from '../lib/supabase';
import type { EnergieausweisData } from '../types/csv';

export const fetchCertificateDataSelective = async (id: string, fields: string[]) => {
  if (!id) return null;
  const selectStr = fields.length ? fields.join(', ') : '*';
  const { data, error } = await supabase
    .from('energieausweise')
    .select(selectStr)
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as Partial<EnergieausweisData> | null;
};

export const fetchCertificateData = async (id: string | null) => {
  if (!id) return null;
  const { data, error } = await supabase
    .from('energieausweise')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as EnergieausweisData;
};

export default {
  fetchCertificateDataSelective,
  fetchCertificateData,
};
