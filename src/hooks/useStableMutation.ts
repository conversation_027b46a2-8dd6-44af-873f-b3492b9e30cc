import { useMemo, useRef } from 'react';
import { useMutation, type UseMutationOptions, type UseMutationResult } from '@tanstack/react-query';

/**
 * Custom hook that provides a stable reference to a React Query mutation
 * This prevents unnecessary re-renders when the mutation object is used as a dependency
 */
export function useStableMutation<TData = unknown, TError = Error, TVariables = void, TContext = unknown>(
  options: UseMutationOptions<TData, TError, TVariables, TContext>
): UseMutationResult<TData, TError, TVariables, TContext> {
  const mutation = useMutation(options);
  const stableRef = useRef(mutation);

  // Create a stable object that only changes when the mutation's key properties change
  const stableMutation = useMemo(() => {
    const current = mutation;
    const previous = stableRef.current;

    // Check if the mutation has meaningfully changed
    const hasChanged = 
      current.isPending !== previous.isPending ||
      current.isSuccess !== previous.isSuccess ||
      current.isError !== previous.isError ||
      current.data !== previous.data ||
      current.error !== previous.error;

    if (hasChanged) {
      // Create a new stable object with the current mutation's properties
      const newStable = {
        ...current,
        // Ensure functions are stable references
        mutate: current.mutate,
        mutateAsync: current.mutateAsync,
        reset: current.reset,
      };
      stableRef.current = newStable as any;
      return newStable;
    }

    // Return the previous stable reference if nothing meaningful changed
    return previous;
  }, [
    mutation.isPending,
    mutation.isSuccess,
    mutation.isError,
    mutation.data,
    mutation.error,
    mutation.mutate,
    mutation.mutateAsync,
    mutation.reset,
  ]);

  return stableMutation;
}
