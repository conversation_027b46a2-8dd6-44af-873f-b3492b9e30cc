import { useState, useCallback, useMemo } from 'react';

export interface LegalConsentState {
  agb: boolean;
  datenschutz: boolean;
  widerruf: boolean;
  dataAccuracy: boolean;
}

export interface LegalConsentReturn {
  // State
  legalConsent: LegalConsentState;

  // Validation
  isValid: boolean;
  validationErrors: string[];

  // Actions
  updateConsent: (field: keyof LegalConsentState, value: boolean) => void;
  setLegalConsent: React.Dispatch<React.SetStateAction<LegalConsentState>>;

  // Helpers
  getValidationMessage: () => string | null;
}

/**
 * Custom hook for managing legal consent state and validation
 * Handles consent checkboxes, validation logic, and error messages
 */
export const useLegalConsent = (): LegalConsentReturn => {
  // Legal consent state
  const [legalConsent, setLegalConsent] = useState<LegalConsentState>({
    agb: false,
    datenschutz: false,
    widerruf: false,
    dataAccuracy: false
  });

  // Update individual consent field
  const updateConsent = useCallback((field: keyof LegalConsentState, value: boolean) => {
    setLegalConsent(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  // Validation logic
  const isValid = useMemo(() => {
    return legalConsent.agb &&
           legalConsent.datenschutz &&
           legalConsent.widerruf &&
           legalConsent.dataAccuracy;
  }, [legalConsent]);

  // Validation errors
  const validationErrors = useMemo(() => {
    const errors: string[] = [];

    if (!legalConsent.agb) {
      errors.push('Zustimmung zu den AGB erforderlich');
    }
    if (!legalConsent.datenschutz) {
      errors.push('Zustimmung zur Datenschutzerklärung erforderlich');
    }
    if (!legalConsent.widerruf) {
      errors.push('Zustimmung zur Widerrufsbelehrung erforderlich');
    }
    if (!legalConsent.dataAccuracy) {
      errors.push('Bestätigung der Datenrichtigkeit erforderlich');
    }

    return errors;
  }, [legalConsent]);

  // Get validation message for display
  const getValidationMessage = useCallback(() => {
    if (isValid) return null;

    if (validationErrors.length === 1) {
      return validationErrors[0];
    }

    return 'Bitte stimmen Sie allen rechtlichen Bestimmungen zu, um fortzufahren.';
  }, [isValid, validationErrors]);

  // Memoize the return object to prevent new references on every render
  return useMemo(() => ({
    // State
    legalConsent,

    // Validation
    isValid,
    validationErrors,

    // Actions
    updateConsent,
    setLegalConsent,

    // Helpers
    getValidationMessage,
  }), [
    legalConsent,
    isValid,
    validationErrors,
    updateConsent,
    setLegalConsent,
    getValidationMessage
  ]);
};