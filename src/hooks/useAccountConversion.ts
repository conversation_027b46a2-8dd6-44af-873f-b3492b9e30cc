import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getUserEmailFromCertificate } from '../utils/accountConversion';

export interface AccountConversionReturn {
  // State
  showAccountConversion: boolean;
  userEmail: string | null;

  // Actions
  setShowAccountConversion: (show: boolean) => void;
  handleAccountConversionSuccess: (onSuccess: () => void) => void;
  handleAccountConversionClose: (onClose: () => void) => void;
}

/**
 * Custom hook for managing anonymous user account conversion flow
 * Handles modal state, user email fetching, and conversion process
 */
export const useAccountConversion = (
  activeCertificateId: string | null
): AccountConversionReturn => {
  const { isAnonymous } = useAuth();

  // Account conversion modal state
  const [showAccountConversion, setShowAccountConversion] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);

  // Get user email from certificate data for anonymous users
  useEffect(() => {
    let isMounted = true;

    const fetchUserEmail = async () => {
      if (isAnonymous && activeCertificateId && !userEmail) {
        const email = await getUserEmailFromCertificate(activeCertificateId);
        if (isMounted) {
          setUserEmail(email);
        }
      }
    };

    fetchUserEmail();

    return () => {
      isMounted = false;
    };
  }, [isAnonymous, activeCertificateId]); // Remove userEmail from dependencies to prevent re-fetch loops

  // Function to handle account conversion success
  const handleAccountConversionSuccess = useCallback((onSuccess: () => void) => {
    setShowAccountConversion(false);
    // Proceed with the original action after successful account conversion
    onSuccess();
  }, []);

  // Function to handle account conversion modal close (user cancelled)
  const handleAccountConversionClose = useCallback((onClose: () => void) => {
    setShowAccountConversion(false);
    // Execute any cleanup logic
    onClose();
  }, []);

  // Memoize the return object to prevent new references on every render
  return useMemo(() => ({
    // State
    showAccountConversion,
    userEmail,

    // Actions
    setShowAccountConversion,
    handleAccountConversionSuccess,
    handleAccountConversionClose,
  }), [
    showAccountConversion,
    userEmail,
    setShowAccountConversion,
    handleAccountConversionSuccess,
    handleAccountConversionClose
  ]);
};