import { useMemo, useRef, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
// supabase not needed here - selective service used instead
import { certificateQueryKeys } from '../utils/queryKeys';
import { fetchCertificateDataSelective } from '../services/certificateService';
import { type EnergieausweisData } from '../types/csv';

// Stable constant to avoid re-creating the array on each render
const KUNDEN_FIELDS = [
  'Kunden_Anrede', 'Kunden_Vorname', 'Kunden_Nachname', 'Kunden_Strasse',
  'Kunden_Hausnr', 'Kunden_PLZ', 'Kunden_Ort', 'Kunden_email', 'Kunden_telefon'
] as const;

// Helper is pure and stable, defined outside the hook
const getCertificateTypeLabelStatic = (type: string | null | undefined) => {
  switch (type) {
    case 'WG/V':
      return 'Wohngebäude-Verbrauchsausweis';
    case 'WG/B':
      return 'Wohngebäude-Bedarfsausweis';
    case 'NWG/V':
      return 'Nicht-Wohngebäude-Verbrauchsausweis';
    default:
      return 'Nicht angegeben';
  }
};

// Types for section configuration
export interface SectionConfig {
  title: string;
  data: Record<string, any> | null | undefined;
  fields?: string[] | Record<string, string>;
  excludeFields?: string[];
}

export interface SummaryDataReturn {
  // Data
  energieausweisData: EnergieausweisData | null;
  sections: SectionConfig[];

  // Status flags
  isPaymentInProgress: boolean;
  isPaymentFailed: boolean;
  isPaid: boolean;
  isPaymentStatus: boolean;

  // Loading states
  isLoading: boolean;

  // Error states
  isError: boolean;

  // Navigation
  handleBackNavigation: () => void;

  // Certificate type helper
  getCertificateTypeLabel: (type: string | null | undefined) => string;
}

/**
 * Custom hook for managing summary data fetching and processing
 * Handles certificate data and data section configuration
 */
export const useSummaryData = (activeCertificateId: string | null): SummaryDataReturn => {
  const navigate = useNavigate();

  // Memoize certificate ID to prevent unnecessary query key changes
  const memoizedCertificateId = useMemo(() => activeCertificateId, [activeCertificateId]);

  // --- Logging helpers ---
  const mainQueryStartRef = useRef<number | null>(null);

  // Fetch all energieausweis data for the current user with optimized caching
  const { data: energieausweisData, isError, isLoading } = useQuery({
    queryKey: certificateQueryKeys.detail(memoizedCertificateId ?? 'none'),
    queryFn: async () => {
      if (!memoizedCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');
      const t0 = performance.now();
      mainQueryStartRef.current = t0;
      // eslint-disable-next-line no-console
      console.log('[SummaryData] main query START', { certificateId: memoizedCertificateId, t0 });
      // For summary we still need most fields - fetch * for now but keep option for selective fields
      const data = await fetchCertificateDataSelective(memoizedCertificateId, []);
      return data as EnergieausweisData;
    },
    enabled: !!memoizedCertificateId,
    retry: false, // Override global default for this critical query
    staleTime: 15 * 60 * 1000, // 15 minutes - longer cache for summary data
    gcTime: 30 * 60 * 1000, // 30 minutes - keep in cache longer
    refetchOnWindowFocus: false, // Prevent refetch on tab switching
    refetchOnMount: false, // Prevent refetch on component remount
    // Only notify when data or error changes to reduce re-renders from loading state flips
    notifyOnChangeProps: ['data', 'isError'],
  });

  // Payment status checks (memoized to prevent unnecessary re-renders)
  const paymentStatus = useMemo(() => energieausweisData?.status, [energieausweisData?.status]);

  const isPaymentInProgress = useMemo(() => paymentStatus === 'payment_initiated', [paymentStatus]);
  const isPaymentFailed = useMemo(() => Boolean(
    paymentStatus &&
    ['payment_failed', 'payment_disputed', 'payment_expired'].includes(paymentStatus)
  ), [paymentStatus]);
  const isPaid = useMemo(() => paymentStatus === 'payment_complete', [paymentStatus]);

  // Legacy check for backward compatibility (memoized)
  const isPaymentStatus = useMemo(() => Boolean(
    paymentStatus &&
    ['payment_initiated', 'payment_complete', 'payment_failed', 'payment_disputed', 'payment_expired'].includes(paymentStatus)
  ), [paymentStatus]);

  // Stable reference to the helper
  const getCertificateTypeLabel = getCertificateTypeLabelStatic;

  // Function to handle back button navigation based on certificate type
  const handleBackNavigation = useCallback(() => {
    if (!energieausweisData?.certificate_type) {
      // Fallback to objektdaten page if certificate type is not available
      navigate({ to: '/erfassen/objektdaten' });
      return;
    }

    // Navigate based on certificate type following the established routing flow
    if (energieausweisData.certificate_type === 'WG/B') {
      // For WG/B: Navigate back to TwwLueftungPage (since VerbrauchPage is skipped)
      navigate({ to: '/erfassen/tww-lueftung' });
    } else {
      // For WG/V and NWG/V: Navigate back to VerbrauchPage (since TwwLueftungPage is skipped)
      navigate({ to: '/erfassen/verbrauch' });
    }
  }, [energieausweisData?.certificate_type, navigate]);

  // Memoize individual data sections to prevent unnecessary recalculations
  const orderInfo = useMemo(() => ({
    order_number: energieausweisData?.order_number || `EA-${energieausweisData?.id?.slice(-8).toUpperCase()}`
  }), [energieausweisData?.order_number, energieausweisData?.id]);

  const certificateTypeInfo = useMemo(() => ({
    certificate_type: getCertificateTypeLabel(energieausweisData?.certificate_type)
  }), [energieausweisData?.certificate_type]);

  // Memoize processed building details to avoid expensive array operations on every render
  const processedGebaeudedetails2 = useMemo(() => {
    if (!energieausweisData?.gebaeudedetails2) return null;

    const details = { ...energieausweisData.gebaeudedetails2 } as Record<string, any>;

    // Process boeden array if it exists
    if (details.boeden && Array.isArray(details.boeden)) {
      details.boeden.forEach((boden: any, index: number) => {
        const prefix = `Boden${index + 1}`;
        details[prefix] = boden.bezeichnung;
        details[`${prefix}_massiv`] = boden.massiv;
        details[`${prefix}_Kellerdecke`] = boden.uebergang;
        details[`${prefix}_Fläche`] = boden.flaeche;
        details[`${prefix}_Dämmung`] = boden.daemmung;
      });
    }

    // Process daecher array if it exists
    if (details.daecher && Array.isArray(details.daecher)) {
      details.daecher.forEach((dach: any, index: number) => {
        const prefix = `Dach${index + 1}`;
        details[prefix] = dach.bezeichnung;
        details[`${prefix}_massiv`] = dach.massiv;
        details[`${prefix}_Geschossdecke`] = dach.uebergang;
        details[`${prefix}_Fläche`] = dach.flaeche;
        details[`${prefix}_Dämmung`] = dach.daemmung;
      });
    }

    // Process waende array if it exists
    if (details.waende && Array.isArray(details.waende)) {
      details.waende.forEach((wand: any, index: number) => {
        const prefix = `Wand${index + 1}`;
        details[prefix] = wand.bezeichnung;
        details[`${prefix}_massiv`] = wand.massiv;
        details[`${prefix}_Fläche`] = wand.flaeche;
        details[`${prefix}_Dämmung`] = wand.daemmung;
      });
    }

    return details;
  }, [energieausweisData?.gebaeudedetails2]);

  // Memoize processed fenster data
  const processedFensterData = useMemo(() => {
    if (!energieausweisData?.fenster?.fenster) return null;

    const result: Record<string, any> = {};
    energieausweisData.fenster.fenster.forEach((fenster: any, index: number) => {
      const prefix = `Fenster${index + 1}_`;
      result[`${prefix}bezeichnung`] = fenster.bezeichnung;
      result[`${prefix}art`] = fenster.art;
      result[`${prefix}flaeche`] = fenster.flaeche;
      result[`${prefix}ausrichtung`] = fenster.ausrichtung;
      result[`${prefix}baujahr`] = fenster.baujahr;
    });
    return result;
  }, [energieausweisData?.fenster?.fenster]);

  // Memoize combined TWW & Lüftung data
  const combinedTwwLueftungData = useMemo(() => ({
    ...(energieausweisData?.trinkwarmwasser || {}),
    ...(energieausweisData?.lueftung || {})
  }), [energieausweisData?.trinkwarmwasser, energieausweisData?.lueftung]);

  // Organize data into sections with optimized dependencies
  const sections: SectionConfig[] = useMemo(() => {
    if (!energieausweisData) return [];

    const baseSections = [
      {
        title: 'Bestellinformationen',
        data: orderInfo,
        fields: { order_number: 'Bestellnummer' } as Record<string, string>,
        excludeFields: []
      },
      {
        title: 'Ausweistyp',
        data: certificateTypeInfo,
        fields: { certificate_type: 'Energieausweistyp' } as Record<string, string>,
        excludeFields: []
      },
      {
        title: 'Objektdaten',
        data: energieausweisData.objektdaten,
        excludeFields: [...KUNDEN_FIELDS]
      },
      {
        title: 'Kundendaten',
        data: energieausweisData.objektdaten,
        fields: [...KUNDEN_FIELDS]
      },
      {
        title: 'Gebäudedetails (Teil 1)',
        data: energieausweisData.gebaeudedetails1
      },
      {
        title: 'Gebäudedetails (Teil 2)',
        data: processedGebaeudedetails2,
        excludeFields: ['boeden', 'daecher', 'waende']
      }
    ];

    // Add conditional sections based on certificate type
    if (energieausweisData.certificate_type === 'WG/B') {
      baseSections.push(
        {
          title: 'Fenster',
          data: processedFensterData
        },
        {
          title: 'Heizung',
          data: energieausweisData.heizung
        },
        {
          title: 'Trinkwarmwasser & Lüftung',
          data: combinedTwwLueftungData
        }
      );
    }

    if (energieausweisData.certificate_type === 'WG/V' || energieausweisData.certificate_type === 'NWG/V') {
      baseSections.push({
        title: 'Verbrauchsdaten',
        data: energieausweisData.verbrauchsdaten
      });
    }

    return baseSections;
  }, [
    // Use more specific dependencies to prevent unnecessary recalculations
    energieausweisData?.id, // Only recalculate if certificate changes
    energieausweisData?.certificate_type,
    energieausweisData?.status, // Include status for payment-related sections
    // Use JSON.stringify for complex objects to ensure proper comparison
    JSON.stringify(energieausweisData?.objektdaten),
    JSON.stringify(energieausweisData?.gebaeudedetails1),
    JSON.stringify(processedGebaeudedetails2),
    JSON.stringify(processedFensterData),
    JSON.stringify(energieausweisData?.heizung),
    JSON.stringify(combinedTwwLueftungData),
    JSON.stringify(energieausweisData?.verbrauchsdaten)
  ]);

  // Memoize the return object to prevent new references on every render
  return useMemo(() => ({
    // Data
    energieausweisData: energieausweisData || null,
    sections,

    // Status flags
    isPaymentInProgress,
    isPaymentFailed,
    isPaid,
    isPaymentStatus,

    // Loading states
    isLoading,

    // Error states
    isError,

    // Navigation
    handleBackNavigation,

    // Certificate type helper
    getCertificateTypeLabel,
  }), [
    energieausweisData,
    sections,
    isPaymentInProgress,
    isPaymentFailed,
    isPaid,
    isPaymentStatus,
    isLoading,
    isError,
    handleBackNavigation,
    getCertificateTypeLabel
  ]);
};